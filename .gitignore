# Localhost File Tailer - Git Ignore File

# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.production
.env.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Logs and databases
*.log
*.sql
*.sqlite
*.sqlite3
*.db

# Test log files (but keep sample fixtures)
implementation/*.log
implementation/test*.log
implementation/sample*.log
implementation/debug*.log
implementation/large*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# Backup files
*.bak
*.backup
*.old

# Runtime files
*.pid
*.lock

# Build artifacts
build/
dist/
out/

# Package files
*.tar.gz
*.zip
*.rar

# Local configuration files
config/local.json
config/development.json
config/production.json

# Test artifacts
test-results/
screenshots/
videos/

# Performance monitoring
.clinic/

# Webpack
.webpack/

# ESLint
.eslintcache

# Prettier
.prettierignore

# Local development files
local/
dev/

# Documentation build
docs/_build/

# Python (if any Python scripts are added)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Java (if any Java components are added)
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Rust (if any Rust components are added)
target/
Cargo.lock

# Go (if any Go components are added)
*.exe
*.exe~
*.dll
*.so
*.dylib
vendor/

# Keep important files
!implementation/tests/fixtures/*.log
!README.md
!package.json
!package-lock.json
