/**
 * Unit tests for localtail_seek_relative_time() function
 * Following TDD approach - these tests define the expected behavior for time-based seeking
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import { localtail_seek_relative_time } from '../../src/localtail.js';
import fs from 'fs/promises';

describe('localtail_seek_relative_time()', () => {
  let testFilePath;
  let tailers = [];

  beforeEach(async () => {
    // Create a test file with timestamped content
    const timestampedContent = global.testUtils.generateTimestampedContent(20, 1000);
    testFilePath = await global.testUtils.createTestFile('timestamped.log', timestampedContent);
    tailers = [];
  });

  afterEach(async () => {
    // Clean up all tailers to prevent unhandled errors
    for (const tailer of tailers) {
      if (tailer && tailer.close) {
        tailer.close();
      }
    }
    tailers = [];
    await global.testUtils.cleanupTestFiles();
  });

  describe('Basic time seeking functionality', () => {
    test('should throw error for non-existent file', async () => {
      const nonExistentPath = '/path/to/nonexistent/file.log';
      
      await expect(localtail_seek_relative_time(nonExistentPath, '5 minutes ago'))
        .rejects.toThrow('File not found');
    });

    test('should throw error for invalid time expression', async () => {
      await expect(localtail_seek_relative_time(testFilePath, 'invalid time'))
        .rejects.toThrow('Invalid time expression');
      
      await expect(localtail_seek_relative_time(testFilePath, ''))
        .rejects.toThrow('Invalid time expression');
      
      await expect(localtail_seek_relative_time(testFilePath, null))
        .rejects.toThrow('Invalid time expression');
    });

    test('should seek to relative time with "minutes ago" format', async () => {
      const result = await localtail_seek_relative_time(testFilePath, '10 minutes ago');
      tailers.push(result);
      
      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('seekTime');
      expect(result).toHaveProperty('position');
      expect(result.seekTime).toBeInstanceOf(Date);
    });

    test('should seek to relative time with "seconds ago" format', async () => {
      const result = await localtail_seek_relative_time(testFilePath, '30 seconds ago');
      tailers.push(result);
      
      expect(result).toHaveProperty('content');
      expect(result.content.length).toBeGreaterThan(0);
    });

    test('should handle "last X minutes" format', async () => {
      const result = await localtail_seek_relative_time(testFilePath, 'last 5 minutes');
      tailers.push(result);
      
      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('timeRange');
      expect(result.timeRange).toHaveProperty('start');
      expect(result.timeRange).toHaveProperty('end');
    });
  });

  describe('Performance requirements', () => {
    test('should seek within 100ms for files larger than 1MB', async () => {
      // Create a large timestamped file
      const largeContent = global.testUtils.generateLargeContent(1.5); // 1.5MB
      const largeFilePath = await global.testUtils.createTestFile('large-timestamped.log', largeContent);
      
      const { result, durationMs } = await global.testUtils.measureTime(async () => {
        return await localtail_seek_relative_time(largeFilePath, '10 minutes ago');
      });
      
      tailers.push(result);
      expect(result).toHaveProperty('content');
      expect(durationMs).toBeLessThan(100); // Must be under 100ms
    });

    test('should efficiently seek to end of large files', async () => {
      const largeContent = global.testUtils.generateLargeContent(2); // 2MB
      const largeFilePath = await global.testUtils.createTestFile('large-timestamped.log', largeContent);
      
      const { result, durationMs } = await global.testUtils.measureTime(async () => {
        return await localtail_seek_relative_time(largeFilePath, 'last 1 minute');
      });
      
      tailers.push(result);
      expect(durationMs).toBeLessThan(100);
      expect(result.content.length).toBeGreaterThan(0);
    });
  });

  describe('Timestamp injection for files without timestamps', () => {
    test('should inject timestamps for files without native timestamps', async () => {
      // Create a file without timestamps
      const noTimestampContent = Array.from({ length: 10 }, (_, i) => `Log line ${i + 1}`).join('\n');
      const noTimestampPath = await global.testUtils.createTestFile('no-timestamps.log', noTimestampContent);
      
      const result = await localtail_seek_relative_time(noTimestampPath, '5 minutes ago');
      tailers.push(result);
      
      expect(result).toHaveProperty('timestampsInjected', true);
      expect(result).toHaveProperty('content');
      expect(result.content.length).toBeGreaterThan(0);
    });

    test('should maintain timestamp index for efficient seeking', async () => {
      const noTimestampContent = Array.from({ length: 100 }, (_, i) => `Log line ${i + 1}`).join('\n');
      const noTimestampPath = await global.testUtils.createTestFile('no-timestamps.log', noTimestampContent);
      
      // First seek should build the index
      const result1 = await localtail_seek_relative_time(noTimestampPath, '10 minutes ago');
      tailers.push(result1);
      
      // Second seek should use the existing index and be faster
      const { result: result2, durationMs } = await global.testUtils.measureTime(async () => {
        return await localtail_seek_relative_time(noTimestampPath, '5 minutes ago');
      });
      
      tailers.push(result2);
      expect(durationMs).toBeLessThan(50); // Should be very fast with index
      expect(result2).toHaveProperty('indexUsed', true);
    });
  });

  describe('Edge cases and error handling', () => {
    test('should handle seeking beyond file start', async () => {
      const result = await localtail_seek_relative_time(testFilePath, '1 hour ago');
      tailers.push(result);
      
      expect(result).toHaveProperty('content');
      expect(result).toHaveProperty('warning');
      expect(result.warning).toContain('Seeking beyond file start');
    });

    test('should handle seeking to future time', async () => {
      await expect(localtail_seek_relative_time(testFilePath, '10 minutes from now'))
        .rejects.toThrow('Cannot seek to future time');
    });

    test('should handle empty files', async () => {
      const emptyFilePath = await global.testUtils.createTestFile('empty.log', '');
      
      const result = await localtail_seek_relative_time(emptyFilePath, '5 minutes ago');
      tailers.push(result);
      
      expect(result).toHaveProperty('content', '');
      expect(result).toHaveProperty('warning');
      expect(result.warning).toContain('File is empty');
    });

    test('should handle binary files', async () => {
      const binaryContent = Buffer.from([0x00, 0x01, 0x02, 0x03, 0xFF, 0xFE]);
      const binaryPath = await global.testUtils.createTestFile('binary.dat', binaryContent);
      
      await expect(localtail_seek_relative_time(binaryPath, '5 minutes ago'))
        .rejects.toThrow('Cannot seek in binary files');
    });
  });

  describe('Time parsing and formats', () => {
    test('should parse various time formats', async () => {
      const formats = [
        '5 minutes ago',
        '30 seconds ago',
        '2 hours ago',
        '1 day ago',
        'last 10 minutes',
        'last 1 hour',
        'last 30 seconds'
      ];
      
      for (const format of formats) {
        const result = await localtail_seek_relative_time(testFilePath, format);
        tailers.push(result);
        expect(result).toHaveProperty('content');
        expect(result).toHaveProperty('seekTime');
      }
    });

    test('should handle mixed case and extra whitespace', async () => {
      const result = await localtail_seek_relative_time(testFilePath, '  5 MINUTES AGO  ');
      tailers.push(result);
      
      expect(result).toHaveProperty('content');
    });
  });

  describe('Integration with localtail', () => {
    test('should work with existing localtail instance', async () => {
      const { localtail } = await import('../../src/localtail.js');
      const tailer = await localtail(testFilePath);
      tailers.push(tailer);
      
      const seekResult = await localtail_seek_relative_time(testFilePath, '5 minutes ago');
      tailers.push(seekResult);
      
      expect(seekResult).toHaveProperty('content');
      expect(seekResult.content.length).toBeLessThanOrEqual(tailer.content.length);
    });
  });
});
