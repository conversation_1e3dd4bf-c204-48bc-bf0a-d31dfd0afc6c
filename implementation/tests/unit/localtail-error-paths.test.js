import { jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { FileTailer, localtail, localtail_seek_relative_time } from '../../src/localtail.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

describe('LocalTail Error Paths', () => {
  let testFilePath;

  beforeEach(async () => {
    testFilePath = path.join(__dirname, '../../test-error-localtail.log');
    
    // Clean up any existing test files
    try {
      await fs.unlink(testFilePath);
    } catch (error) {
      // File doesn't exist, that's fine
    }
  });

  afterEach(async () => {
    // Clean up test files
    try {
      await fs.unlink(testFilePath);
    } catch (error) {
      // File doesn't exist, that's fine
    }
  });

  describe('FileTailer Constructor and Initialization Errors', () => {
    test('should throw error for invalid file path - empty string', async () => {
      await expect(async () => {
        const tailer = new FileTailer('');
        await tailer.initialize();
      }).rejects.toThrow('Invalid file path');
    });

    test('should throw error for invalid file path - null', async () => {
      await expect(async () => {
        const tailer = new FileTailer(null);
        await tailer.initialize();
      }).rejects.toThrow('Invalid file path');
    });

    test('should throw error for invalid file path - undefined', async () => {
      await expect(async () => {
        const tailer = new FileTailer(undefined);
        await tailer.initialize();
      }).rejects.toThrow('Invalid file path');
    });

    test('should throw error for invalid file path - non-string', async () => {
      await expect(async () => {
        const tailer = new FileTailer(123);
        await tailer.initialize();
      }).rejects.toThrow('Invalid file path');
    });

    test('should throw error for invalid file path - whitespace only', async () => {
      await expect(async () => {
        const tailer = new FileTailer('   ');
        await tailer.initialize();
      }).rejects.toThrow('Invalid file path');
    });

    test('should throw "File not found" for non-existent file', async () => {
      await expect(async () => {
        const tailer = new FileTailer('non-existent-file.log');
        await tailer.initialize();
      }).rejects.toThrow('File not found');
    });

    test('should handle permission denied errors', async () => {
      // Mock fs.stat to throw permission error
      const originalStat = fs.stat;
      fs.stat = jest.fn().mockRejectedValue(Object.assign(new Error('Permission denied'), { code: 'EACCES' }));

      await expect(async () => {
        const tailer = new FileTailer('permission-denied.log');
        await tailer.initialize();
      }).rejects.toThrow('Permission denied');

      // Restore original function
      fs.stat = originalStat;
    });
  });

  describe('File Reading Errors', () => {
    test('should handle file read errors gracefully', async () => {
      // Create a file first
      await fs.writeFile(testFilePath, 'Test content');

      // Mock fs.readFile to throw an error
      const originalReadFile = fs.readFile;
      fs.readFile = jest.fn().mockRejectedValue(new Error('Read permission denied'));

      await expect(async () => {
        const tailer = new FileTailer(testFilePath);
        await tailer.initialize();
      }).rejects.toThrow('Failed to read file: Read permission denied');

      // Restore original function
      fs.readFile = originalReadFile;
    });
  });

  describe('File Watching Errors', () => {
    test('should handle file deletion during watching', async () => {
      // Create a file and start watching
      await fs.writeFile(testFilePath, 'Initial content');
      
      const tailer = new FileTailer(testFilePath);
      await tailer.initialize();

      let errorEmitted = false;
      let errorMessage = '';

      tailer.on('error', (error) => {
        errorEmitted = true;
        errorMessage = error.message;
      });

      // Simulate file deletion by emitting unlink event
      tailer.watcher.emit('unlink');

      // Wait a bit for the event to be processed
      await global.testUtils.wait(50);

      expect(errorEmitted).toBe(true);
      expect(errorMessage).toBe('File no longer exists');

      tailer.close();
    });

    test('should handle file change errors gracefully', async () => {
      // Create a file and start watching
      await fs.writeFile(testFilePath, 'Initial content');
      
      const tailer = new FileTailer(testFilePath);
      await tailer.initialize();

      // Mock handleFileChange to throw an error
      const originalHandleFileChange = tailer.handleFileChange;
      tailer.handleFileChange = jest.fn().mockRejectedValue(new Error('File change error'));

      let errorEmitted = false;
      let errorMessage = '';

      tailer.on('error', (error) => {
        errorEmitted = true;
        errorMessage = error.message;
      });

      // Simulate file change
      tailer.watcher.emit('change');

      // Wait a bit for the event to be processed
      await global.testUtils.wait(50);

      expect(errorEmitted).toBe(true);
      expect(errorMessage).toBe('File change error');

      // Restore original function
      tailer.handleFileChange = originalHandleFileChange;
      tailer.close();
    });

    test('should not emit error when no listeners are present', async () => {
      // Create a file and start watching
      await fs.writeFile(testFilePath, 'Initial content');
      
      const tailer = new FileTailer(testFilePath);
      await tailer.initialize();

      // Mock handleFileChange to throw an error
      tailer.handleFileChange = jest.fn().mockRejectedValue(new Error('File change error'));

      // Don't add any error listeners

      // Simulate file change - should not throw unhandled error
      expect(() => {
        tailer.watcher.emit('change');
      }).not.toThrow();

      tailer.close();
    });
  });

  describe('getUpdates Errors', () => {
    test('should return error object when file no longer exists', async () => {
      // Create a file and start watching
      await fs.writeFile(testFilePath, 'Initial content');
      
      const tailer = new FileTailer(testFilePath);
      await tailer.initialize();

      // Delete the file
      await fs.unlink(testFilePath);

      // Try to get updates
      const result = await tailer.getUpdates();

      expect(result.error).toBeDefined();
      expect(result.error).toContain('File no longer exists');

      tailer.close();
    });
  });

  // Note: Stream reading errors are difficult to test reliably in Jest environment
  // The readNewContent method uses fs.createReadStream which is hard to mock properly
  // This error path is covered by integration tests when files are deleted during reading

  describe('localtail_seek_relative_time Error Paths', () => {
    test('should throw error for invalid file path - empty string', async () => {
      await expect(
        localtail_seek_relative_time('', 'last 1 minute')
      ).rejects.toThrow('Invalid file path');
    });

    test('should throw error for invalid file path - null', async () => {
      await expect(
        localtail_seek_relative_time(null, 'last 1 minute')
      ).rejects.toThrow('Invalid file path');
    });

    test('should throw error for invalid file path - whitespace only', async () => {
      await expect(
        localtail_seek_relative_time('   ', 'last 1 minute')
      ).rejects.toThrow('Invalid file path');
    });

    test('should throw "File not found" for non-existent file', async () => {
      await expect(
        localtail_seek_relative_time('non-existent-file.log', 'last 1 minute')
      ).rejects.toThrow('File not found');
    });

    test('should throw error for binary files', async () => {
      // Create a binary file (with null bytes)
      const binaryContent = Buffer.from([0x00, 0x01, 0x02, 0x03, 0x00, 0xFF]);
      await fs.writeFile(testFilePath, binaryContent);

      await expect(
        localtail_seek_relative_time(testFilePath, 'last 1 minute')
      ).rejects.toThrow('Cannot seek in binary files');
    });

    test('should throw error for invalid time expression', async () => {
      await fs.writeFile(testFilePath, 'Test content');

      await expect(
        localtail_seek_relative_time(testFilePath, 'invalid expression')
      ).rejects.toThrow('Invalid time expression');
    });

    test('should throw error for future time expression', async () => {
      await fs.writeFile(testFilePath, 'Test content');

      await expect(
        localtail_seek_relative_time(testFilePath, '1 hour from now')
      ).rejects.toThrow('Cannot seek to future time');
    });

    test('should throw error for null time expression', async () => {
      await fs.writeFile(testFilePath, 'Test content');

      await expect(
        localtail_seek_relative_time(testFilePath, null)
      ).rejects.toThrow('Invalid time expression');
    });

    test('should throw error for non-string time expression', async () => {
      await fs.writeFile(testFilePath, 'Test content');

      await expect(
        localtail_seek_relative_time(testFilePath, 123)
      ).rejects.toThrow('Invalid time expression');
    });

    test('should handle file permission errors', async () => {
      // Mock fs.stat to throw permission error
      const originalStat = fs.stat;
      fs.stat = jest.fn().mockRejectedValue(Object.assign(new Error('Permission denied'), { code: 'EACCES' }));

      await expect(
        localtail_seek_relative_time('permission-denied.log', 'last 1 minute')
      ).rejects.toThrow('Permission denied');

      // Restore original function
      fs.stat = originalStat;
    });
  });

  describe('localtail Function Error Paths', () => {
    test('should handle initialization errors in localtail wrapper', async () => {
      await expect(
        localtail('non-existent-file.log')
      ).rejects.toThrow('File not found');
    });

    test('should handle invalid file path in localtail wrapper', async () => {
      await expect(
        localtail('')
      ).rejects.toThrow('Invalid file path');
    });
  });
});
