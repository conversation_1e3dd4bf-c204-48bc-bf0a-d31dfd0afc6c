import request from 'supertest';
import { jest } from '@jest/globals';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the app for testing
let app;

describe('Server Error Paths', () => {
  let testFilePath;

  beforeAll(async () => {
    // Import app after setting up test environment
    const serverModule = await import('../../server.js');
    app = serverModule.default || serverModule.app;
    
    testFilePath = path.join(__dirname, '../../test-error-paths.log');
  });

  beforeEach(async () => {
    // Clean up any existing test files
    try {
      await fs.unlink(testFilePath);
    } catch (error) {
      // File doesn't exist, that's fine
    }
  });

  afterEach(async () => {
    // Stop any active tailers
    try {
      await request(app)
        .post('/api/tail/stop')
        .send({ filePath: testFilePath });
    } catch (error) {
      // Ignore errors during cleanup
    }

    // Clean up test files
    try {
      await fs.unlink(testFilePath);
    } catch (error) {
      // File doesn't exist, that's fine
    }
  });

  describe('API Input Validation Errors', () => {
    test('should return 400 for missing file path in start', async () => {
      const response = await request(app)
        .post('/api/tail/start')
        .send({})
        .expect(400);

      expect(response.body.error).toBe('File path is required');
    });

    test('should return 400 for empty file path in start', async () => {
      const response = await request(app)
        .post('/api/tail/start')
        .send({ filePath: '' })
        .expect(400);

      expect(response.body.error).toBe('File path is required');
    });

    test('should return 400 for missing file path in seek', async () => {
      const response = await request(app)
        .post('/api/tail/seek')
        .send({ timeExpression: 'last 1 minute' })
        .expect(400);

      expect(response.body.error).toBe('File path is required');
    });

    test('should return 400 for missing time expression in seek', async () => {
      const response = await request(app)
        .post('/api/tail/seek')
        .send({ filePath: 'test.log' })
        .expect(400);

      expect(response.body.error).toBe('Time expression is required');
    });

    test('should return 400 for empty time expression in seek', async () => {
      const response = await request(app)
        .post('/api/tail/seek')
        .send({ filePath: 'test.log', timeExpression: '' })
        .expect(400);

      expect(response.body.error).toBe('Time expression is required');
    });
  });

  describe('File System Errors', () => {
    test('should return 500 for non-existent file in start', async () => {
      const response = await request(app)
        .post('/api/tail/start')
        .send({ filePath: 'non-existent-file.log' })
        .expect(500);

      expect(response.body.error).toContain('ENOENT');
    });

    test('should return 404 for updates on non-active tailer', async () => {
      const response = await request(app)
        .get('/api/tail/updates/non-active-file.log')
        .expect(404);

      expect(response.body.error).toBe('No active tailer for this file');
    });

    test('should handle file deletion during tailing', async () => {
      // Create and start tailing a file
      await fs.writeFile(testFilePath, 'Initial content\n');
      
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Delete the file
      await fs.unlink(testFilePath);

      // Try to get updates - should return error
      const response = await request(app)
        .get(`/api/tail/updates/${encodeURIComponent(testFilePath)}`)
        .expect(500);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('Time Expression Parsing Errors', () => {
    test('should return 500 for invalid time expression format', async () => {
      // Create a file and start tailing
      await fs.writeFile(testFilePath, 'Test content\n');
      
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      const response = await request(app)
        .post('/api/tail/seek')
        .send({ 
          filePath: testFilePath, 
          timeExpression: 'invalid format' 
        })
        .expect(500);

      expect(response.body.error).toBe('Invalid time expression');
    });

    test('should return 500 for future time expression', async () => {
      // Create a file and start tailing
      await fs.writeFile(testFilePath, 'Test content\n');
      
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      const response = await request(app)
        .post('/api/tail/seek')
        .send({ 
          filePath: testFilePath, 
          timeExpression: '1 hour from now' 
        })
        .expect(500);

      expect(response.body.error).toBe('Cannot seek to future time');
    });

    test('should return 500 for null time expression', async () => {
      // Create a file and start tailing
      await fs.writeFile(testFilePath, 'Test content\n');
      
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      const response = await request(app)
        .post('/api/tail/seek')
        .send({ 
          filePath: testFilePath, 
          timeExpression: null 
        })
        .expect(400);

      expect(response.body.error).toBe('Time expression is required');
    });

    test('should return 500 for non-string time expression', async () => {
      // Create a file and start tailing
      await fs.writeFile(testFilePath, 'Test content\n');
      
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      const response = await request(app)
        .post('/api/tail/seek')
        .send({ 
          filePath: testFilePath, 
          timeExpression: 123 
        })
        .expect(500);

      expect(response.body.error).toBe('Invalid time expression');
    });
  });

  describe('Seek Without Active Tailing', () => {
    test('should return 400 when seeking file not being tailed', async () => {
      const response = await request(app)
        .post('/api/tail/seek')
        .send({ 
          filePath: 'not-tailed-file.log', 
          timeExpression: 'last 1 minute' 
        })
        .expect(400);

      expect(response.body.error).toBe('File is not being actively tailed. Please start tailing first.');
    });
  });

  describe('HTTP Error Handlers', () => {
    test('should return 404 for unknown endpoints', async () => {
      const response = await request(app)
        .get('/api/unknown-endpoint')
        .expect(404);

      expect(response.body.error).toBe('Not found');
    });

    test('should return 404 for unknown POST endpoints', async () => {
      const response = await request(app)
        .post('/api/unknown-endpoint')
        .send({})
        .expect(404);

      expect(response.body.error).toBe('Not found');
    });
  });

  describe('File List Errors', () => {
    test('should handle directory read errors gracefully', async () => {
      // Mock fs.readdir to throw an error
      const originalReaddir = fs.readdir;
      fs.readdir = jest.fn().mockRejectedValue(new Error('Permission denied'));

      const response = await request(app)
        .get('/api/files')
        .expect(500);

      expect(response.body.error).toBe('Permission denied');

      // Restore original function
      fs.readdir = originalReaddir;
    });
  });
});
