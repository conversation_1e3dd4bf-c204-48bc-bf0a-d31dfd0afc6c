/**
 * Integration tests for WebSocket functionality
 * Tests real-time file update broadcasting via WebSocket connections
 */

import { describe, test, expect, beforeEach, afterEach, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import WebSocket from 'ws';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// Import the actual server app
import app from '../../server.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let testFilePath;
const wsPort = 3000; // Use the same port as the HTTP server

describe('WebSocket Integration', () => {
  // No need to start a separate server - the app import already starts it

  beforeEach(async () => {
    // Create a test file for each test
    testFilePath = await global.testUtils.createTestFile('websocket-test.log', 
      'Initial line 1\nInitial line 2\nInitial line 3\n');
  });

  afterEach(async () => {
    // Stop any active tailing to clean up
    try {
      await request(app)
        .post('/api/tail/stop')
        .send({ filePath: testFilePath });
    } catch (error) {
      // Ignore cleanup errors
    }
    
    // Clean up test files
    await global.testUtils.cleanupTestFiles();
  });

  describe('WebSocket Server Setup', () => {
    test('should have WebSocket server running on same port as HTTP server', async () => {
      const ws = new WebSocket(`ws://localhost:${wsPort}`);
      
      await new Promise((resolve, reject) => {
        ws.on('open', resolve);
        ws.on('error', reject);
        setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
      });

      expect(ws.readyState).toBe(WebSocket.OPEN);
      ws.close();
    });

    test('should handle multiple concurrent WebSocket connections', async () => {
      const connections = [];
      const connectionCount = 5;

      // Create multiple connections
      for (let i = 0; i < connectionCount; i++) {
        const ws = new WebSocket(`ws://localhost:${wsPort}`);
        connections.push(ws);
        
        await new Promise((resolve, reject) => {
          ws.on('open', resolve);
          ws.on('error', reject);
          setTimeout(() => reject(new Error(`WebSocket connection ${i} timeout`)), 5000);
        });
      }

      // Verify all connections are open
      connections.forEach((ws, index) => {
        expect(ws.readyState).toBe(WebSocket.OPEN);
      });

      // Close all connections
      connections.forEach(ws => ws.close());
    });

    test('should handle WebSocket connection errors gracefully', async () => {
      // Try to connect to wrong port
      const ws = new WebSocket(`ws://localhost:${wsPort + 1000}`);
      
      await expect(new Promise((resolve, reject) => {
        ws.on('open', () => reject(new Error('Should not connect')));
        ws.on('error', resolve);
        setTimeout(() => reject(new Error('Error timeout')), 5000);
      })).resolves.toBeDefined();
    });
  });

  describe('Real-time File Update Broadcasting', () => {
    test('should broadcast file updates to connected WebSocket clients', async () => {
      const ws = new WebSocket(`ws://localhost:${wsPort}`);
      const messages = [];

      // Set up message collection
      ws.on('message', (data) => {
        messages.push(JSON.parse(data.toString()));
      });

      // Wait for connection
      await new Promise((resolve, reject) => {
        ws.on('open', resolve);
        ws.on('error', reject);
        setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
      });

      // Start tailing the file
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Wait a moment for setup
      await global.testUtils.wait(100);

      // Add new content to the file
      const newContent = 'New line 4\nNew line 5\n';
      await fs.appendFile(testFilePath, newContent);

      // Wait for the update to be broadcast
      await global.testUtils.wait(500);

      // Verify we received the update via WebSocket
      expect(messages.length).toBeGreaterThan(0);
      
      const updateMessage = messages.find(msg => msg.type === 'fileUpdate');
      expect(updateMessage).toBeDefined();
      expect(updateMessage.filePath).toBe(testFilePath);
      expect(updateMessage.newContent).toContain('New line 4');
      expect(updateMessage.newContent).toContain('New line 5');

      ws.close();
    });

    test('should broadcast to multiple clients simultaneously', async () => {
      const clients = [];
      const clientMessages = [];
      const clientCount = 3;

      // Create multiple WebSocket clients
      for (let i = 0; i < clientCount; i++) {
        const ws = new WebSocket(`ws://localhost:${wsPort}`);
        const messages = [];
        
        ws.on('message', (data) => {
          messages.push(JSON.parse(data.toString()));
        });

        clients.push(ws);
        clientMessages.push(messages);

        await new Promise((resolve, reject) => {
          ws.on('open', resolve);
          ws.on('error', reject);
          setTimeout(() => reject(new Error(`Client ${i} connection timeout`)), 5000);
        });
      }

      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      await global.testUtils.wait(100);

      // Add new content
      const newContent = 'Broadcast test line\n';
      await fs.appendFile(testFilePath, newContent);

      // Wait for broadcasts
      await global.testUtils.wait(500);

      // Verify all clients received the update
      clientMessages.forEach((messages, index) => {
        expect(messages.length).toBeGreaterThan(0);
        
        const updateMessage = messages.find(msg => msg.type === 'fileUpdate');
        expect(updateMessage).toBeDefined();
        expect(updateMessage.newContent).toContain('Broadcast test line');
      });

      // Clean up
      clients.forEach(ws => ws.close());
    });

    test('should handle client disconnections gracefully', async () => {
      const ws1 = new WebSocket(`ws://localhost:${wsPort}`);
      const ws2 = new WebSocket(`ws://localhost:${wsPort}`);
      const messages2 = [];

      ws2.on('message', (data) => {
        messages2.push(JSON.parse(data.toString()));
      });

      // Wait for connections
      await Promise.all([
        new Promise((resolve, reject) => {
          ws1.on('open', resolve);
          ws1.on('error', reject);
          setTimeout(() => reject(new Error('WS1 connection timeout')), 5000);
        }),
        new Promise((resolve, reject) => {
          ws2.on('open', resolve);
          ws2.on('error', reject);
          setTimeout(() => reject(new Error('WS2 connection timeout')), 5000);
        })
      ]);

      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      await global.testUtils.wait(100);

      // Disconnect first client
      ws1.close();
      await global.testUtils.wait(100);

      // Add new content - should still reach second client
      const newContent = 'After disconnect test\n';
      await fs.appendFile(testFilePath, newContent);

      await global.testUtils.wait(500);

      // Verify second client still receives updates
      expect(messages2.length).toBeGreaterThan(0);
      const updateMessage = messages2.find(msg => msg.type === 'fileUpdate');
      expect(updateMessage).toBeDefined();
      expect(updateMessage.newContent).toContain('After disconnect test');

      ws2.close();
    });
  });

  describe('WebSocket Message Protocol', () => {
    test('should send structured messages with correct event types', async () => {
      const ws = new WebSocket(`ws://localhost:${wsPort}`);
      const messages = [];

      ws.on('message', (data) => {
        messages.push(JSON.parse(data.toString()));
      });

      await new Promise((resolve, reject) => {
        ws.on('open', resolve);
        ws.on('error', reject);
        setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
      });

      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      await global.testUtils.wait(100);

      // Add content to trigger update
      await fs.appendFile(testFilePath, 'Protocol test line\n');
      await global.testUtils.wait(500);

      // Verify message structure
      const updateMessage = messages.find(msg => msg.type === 'fileUpdate');
      expect(updateMessage).toBeDefined();
      expect(updateMessage).toHaveProperty('type', 'fileUpdate');
      expect(updateMessage).toHaveProperty('filePath');
      expect(updateMessage).toHaveProperty('newContent');
      expect(updateMessage).toHaveProperty('timestamp');
      expect(updateMessage).toHaveProperty('position');
      expect(updateMessage).toHaveProperty('size');

      // Verify totalContent is NOT included in WebSocket messages (performance optimization)
      expect(updateMessage).not.toHaveProperty('totalContent');

      ws.close();
    });

    test('should handle invalid WebSocket messages gracefully', async () => {
      const ws = new WebSocket(`ws://localhost:${wsPort}`);

      await new Promise((resolve, reject) => {
        ws.on('open', resolve);
        ws.on('error', reject);
        setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
      });

      // Send invalid JSON
      ws.send('invalid json');
      
      // Send valid JSON but invalid message structure
      ws.send(JSON.stringify({ invalidField: 'test' }));

      // Wait to ensure server doesn't crash
      await global.testUtils.wait(200);

      // Verify connection is still alive
      expect(ws.readyState).toBe(WebSocket.OPEN);

      ws.close();
    });

    test('should optimize WebSocket messages by excluding totalContent', async () => {
      const ws = new WebSocket(`ws://localhost:${wsPort}`);
      const messages = [];

      ws.on('message', (data) => {
        messages.push(JSON.parse(data.toString()));
      });

      await new Promise((resolve, reject) => {
        ws.on('open', resolve);
        ws.on('error', reject);
        setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
      });

      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      await global.testUtils.wait(100);

      // Add content to create a larger file
      const largeContent = 'A'.repeat(1000) + '\n'; // 1KB line
      await fs.appendFile(testFilePath, largeContent);
      await global.testUtils.wait(500);

      // Verify WebSocket message is optimized
      const updateMessage = messages.find(msg => msg.type === 'fileUpdate');
      expect(updateMessage).toBeDefined();

      // Should contain only incremental newContent, not totalContent
      expect(updateMessage.newContent).toContain('A'.repeat(1000));
      expect(updateMessage).not.toHaveProperty('totalContent');

      // Verify message size is reasonable (should be ~1KB, not growing with file size)
      const messageSize = JSON.stringify(updateMessage).length;
      expect(messageSize).toBeLessThan(2000); // Should be much smaller than if totalContent was included

      ws.close();
    });
  });

  describe('Performance and Reliability', () => {
    test('should handle rapid file updates without message loss', async () => {
      const ws = new WebSocket(`ws://localhost:${wsPort}`);
      const messages = [];

      ws.on('message', (data) => {
        messages.push(JSON.parse(data.toString()));
      });

      await new Promise((resolve, reject) => {
        ws.on('open', resolve);
        ws.on('error', reject);
        setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
      });

      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      await global.testUtils.wait(100);

      // Add multiple rapid updates
      const updateCount = 10;
      for (let i = 0; i < updateCount; i++) {
        await fs.appendFile(testFilePath, `Rapid update ${i}\n`);
        await global.testUtils.wait(50); // Small delay between updates
      }

      // Wait for all updates to be processed
      await global.testUtils.wait(1000);

      // Verify we received updates (may be batched)
      const updateMessages = messages.filter(msg => msg.type === 'fileUpdate');
      expect(updateMessages.length).toBeGreaterThan(0);

      // Verify all content was captured
      const allNewContent = updateMessages.map(msg => msg.newContent).join('');
      for (let i = 0; i < updateCount; i++) {
        expect(allNewContent).toContain(`Rapid update ${i}`);
      }

      ws.close();
    });

    test('should maintain WebSocket connection during long sessions', async () => {
      const ws = new WebSocket(`ws://localhost:${wsPort}`);
      let connectionStable = true;

      ws.on('close', () => {
        connectionStable = false;
      });

      ws.on('error', () => {
        connectionStable = false;
      });

      await new Promise((resolve, reject) => {
        ws.on('open', resolve);
        ws.on('error', reject);
        setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
      });

      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Simulate a longer session with periodic updates
      for (let i = 0; i < 5; i++) {
        await fs.appendFile(testFilePath, `Long session update ${i}\n`);
        await global.testUtils.wait(200);
        expect(connectionStable).toBe(true);
        expect(ws.readyState).toBe(WebSocket.OPEN);
      }

      ws.close();
    });
  });
});
