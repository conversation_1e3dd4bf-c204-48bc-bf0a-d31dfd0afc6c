#!/usr/bin/env node

/**
 * Log simulator for testing the file tailer UI
 * Appends new log entries to test files at regular intervals
 */

import fs from 'fs/promises';

const LOG_FILES = ['sample.log', 'test.log'];
const UPDATE_INTERVAL = 2000; // 2 seconds

let counter = 1;
let isRunning = true;

const LOG_LEVELS = ['INFO', 'DEBUG', 'WARN', 'ERROR'];
const ACTIONS = [
  'Processing user request',
  'Database query executed',
  'Cache updated',
  'API response sent',
  'File uploaded',
  'Email notification sent',
  'Background job completed',
  'System health check',
  'Memory usage checked',
  'Disk space monitored'
];

function generateLogEntry(includeTimestamp = true) {
  const timestamp = new Date().toISOString();
  const level = LOG_LEVELS[Math.floor(Math.random() * LOG_LEVELS.length)];
  const action = ACTIONS[Math.floor(Math.random() * ACTIONS.length)];
  
  if (includeTimestamp) {
    return `${timestamp} ${level} ${action} #${counter}`;
  } else {
    return `${action} #${counter} (${level})`;
  }
}

async function appendToFile(filename, content) {
  try {
    await fs.appendFile(filename, content + '\n');
    console.log(`📝 Added to ${filename}: ${content}`);
  } catch (error) {
    console.error(`❌ Error writing to ${filename}:`, error.message);
  }
}

async function simulateLogUpdates() {
  console.log('🚀 Starting log simulation...');
  console.log('📁 Updating files:', LOG_FILES.join(', '));
  console.log('⏱️  Update interval:', UPDATE_INTERVAL + 'ms');
  console.log('🛑 Press Ctrl+C to stop\n');

  while (isRunning) {
    for (const logFile of LOG_FILES) {
      const includeTimestamp = logFile === 'sample.log';
      const logEntry = generateLogEntry(includeTimestamp);
      await appendToFile(logFile, logEntry);
    }
    
    counter++;
    
    // Wait for next update
    await new Promise(resolve => setTimeout(resolve, UPDATE_INTERVAL));
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping log simulation...');
  isRunning = false;
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Stopping log simulation...');
  isRunning = false;
  process.exit(0);
});

// Start simulation
simulateLogUpdates().catch(console.error);
