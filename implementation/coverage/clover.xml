<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755480650618" clover="3.2.0">
  <project timestamp="1755480650618" name="All files">
    <metrics statements="397" coveredstatements="341" conditionals="160" coveredconditionals="130" methods="59" coveredmethods="47" elements="616" coveredelements="518" complexity="0" loc="397" ncloc="397" packages="2" files="2" classes="2"/>
    <package name="implementation">
      <metrics statements="181" coveredstatements="146" conditionals="57" coveredconditionals="44" methods="24" coveredmethods="16"/>
      <file name="server.js" path="/Users/<USER>/repos/personal/exp_web_localtail/implementation/server.js">
        <metrics statements="181" coveredstatements="146" conditionals="57" coveredconditionals="44" methods="24" coveredmethods="16"/>
        <line num="14" count="2" type="stmt"/>
        <line num="15" count="2" type="stmt"/>
        <line num="17" count="2" type="stmt"/>
        <line num="18" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="21" count="2" type="stmt"/>
        <line num="22" count="2" type="stmt"/>
        <line num="25" count="2" type="stmt"/>
        <line num="26" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
        <line num="30" count="85" type="stmt"/>
        <line num="31" count="85" type="stmt"/>
        <line num="32" count="85" type="stmt"/>
        <line num="33" count="85" type="stmt"/>
        <line num="37" count="2" type="stmt"/>
        <line num="40" count="2" type="stmt"/>
        <line num="43" count="2" type="stmt"/>
        <line num="46" count="2" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="55" count="6" type="cond" truecount="4" falsecount="0"/>
        <line num="56" count="1" type="stmt"/>
        <line num="59" count="5" type="stmt"/>
        <line num="60" count="5" type="stmt"/>
        <line num="63" count="5" type="stmt"/>
        <line num="64" count="5" type="cond" truecount="1" falsecount="1"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="78" count="5" type="stmt"/>
        <line num="79" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="80" count="3" type="stmt"/>
        <line num="81" count="3" type="stmt"/>
        <line num="82" count="3" type="stmt"/>
        <line num="89" count="3" type="stmt"/>
        <line num="90" count="3" type="stmt"/>
        <line num="98" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="99" count="1" type="stmt"/>
        <line num="102" count="1" type="stmt"/>
        <line num="106" count="2" type="stmt"/>
        <line num="107" count="1" type="stmt"/>
        <line num="108" count="1" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="119" count="2" type="stmt"/>
        <line num="120" count="19" type="stmt"/>
        <line num="121" count="19" type="stmt"/>
        <line num="122" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="123" count="1" type="stmt"/>
        <line num="126" count="18" type="stmt"/>
        <line num="129" count="18" type="stmt"/>
        <line num="132" count="17" type="cond" truecount="2" falsecount="0"/>
        <line num="133" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="137" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="138" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="141" count="1" type="stmt"/>
        <line num="145" count="17" type="stmt"/>
        <line num="146" count="17" type="stmt"/>
        <line num="149" count="17" type="stmt"/>
        <line num="156" count="17" type="stmt"/>
        <line num="157" count="17" type="stmt"/>
        <line num="163" count="17" type="stmt"/>
        <line num="167" count="55" type="stmt"/>
        <line num="168" count="17" type="stmt"/>
        <line num="170" count="39" type="stmt"/>
        <line num="178" count="17" type="stmt"/>
        <line num="187" count="1" type="stmt"/>
        <line num="192" count="2" type="stmt"/>
        <line num="193" count="7" type="stmt"/>
        <line num="194" count="7" type="stmt"/>
        <line num="195" count="7" type="stmt"/>
        <line num="196" count="7" type="stmt"/>
        <line num="197" count="7" type="stmt"/>
        <line num="199" count="7" type="stmt"/>
        <line num="201" count="7" type="cond" truecount="4" falsecount="0"/>
        <line num="202" count="3" type="stmt"/>
        <line num="203" count="3" type="stmt"/>
        <line num="207" count="4" type="stmt"/>
        <line num="208" count="2" type="stmt"/>
        <line num="210" count="2" type="stmt"/>
        <line num="212" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="214" count="1" type="stmt"/>
        <line num="215" count="1" type="stmt"/>
        <line num="218" count="1" type="stmt"/>
        <line num="219" count="1" type="stmt"/>
        <line num="220" count="1" type="stmt"/>
        <line num="223" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="224" count="1" type="stmt"/>
        <line num="225" count="3" type="stmt"/>
        <line num="226" count="1" type="stmt"/>
        <line num="229" count="1" type="stmt"/>
        <line num="230" count="2" type="stmt"/>
        <line num="238" count="2" type="stmt"/>
        <line num="246" count="2" type="stmt"/>
        <line num="254" count="2" type="stmt"/>
        <line num="255" count="2" type="stmt"/>
        <line num="256" count="2" type="stmt"/>
        <line num="257" count="2" type="stmt"/>
        <line num="262" count="2" type="stmt"/>
        <line num="263" count="35" type="stmt"/>
        <line num="264" count="35" type="stmt"/>
        <line num="265" count="35" type="stmt"/>
        <line num="267" count="35" type="cond" truecount="2" falsecount="0"/>
        <line num="268" count="16" type="stmt"/>
        <line num="269" count="16" type="stmt"/>
        <line num="270" count="16" type="stmt"/>
        <line num="273" count="16" type="cond" truecount="1" falsecount="1"/>
        <line num="274" count="16" type="stmt"/>
        <line num="275" count="16" type="stmt"/>
        <line num="279" count="16" type="stmt"/>
        <line num="282" count="35" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="290" count="2" type="stmt"/>
        <line num="291" count="14" type="stmt"/>
        <line num="292" count="14" type="stmt"/>
        <line num="294" count="14" type="cond" truecount="2" falsecount="0"/>
        <line num="295" count="2" type="stmt"/>
        <line num="298" count="12" type="cond" truecount="2" falsecount="0"/>
        <line num="299" count="4" type="stmt"/>
        <line num="303" count="8" type="stmt"/>
        <line num="304" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="305" count="2" type="stmt"/>
        <line num="311" count="6" type="stmt"/>
        <line num="312" count="3" type="cond" truecount="3" falsecount="0"/>
        <line num="314" count="3" type="stmt"/>
        <line num="315" count="3" type="stmt"/>
        <line num="317" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="319" count="3" type="stmt"/>
        <line num="320" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="323" count="3" type="cond" truecount="1" falsecount="1"/>
        <line num="324" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="330" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="stmt"/>
        <line num="339" count="6" type="stmt"/>
        <line num="341" count="3" type="stmt"/>
        <line num="353" count="3" type="stmt"/>
        <line num="354" count="3" type="stmt"/>
        <line num="359" count="2" type="stmt"/>
        <line num="360" count="7" type="stmt"/>
        <line num="371" count="2" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="stmt"/>
        <line num="377" count="2" type="stmt"/>
        <line num="378" count="2" type="stmt"/>
        <line num="382" count="2" type="stmt"/>
        <line num="383" count="1" type="stmt"/>
        <line num="384" count="1" type="stmt"/>
        <line num="385" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="386" count="1" type="stmt"/>
        <line num="387" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="389" count="1" type="stmt"/>
        <line num="390" count="1" type="stmt"/>
        <line num="391" count="1" type="stmt"/>
        <line num="392" count="1" type="stmt"/>
        <line num="396" count="2" type="stmt"/>
        <line num="397" count="0" type="stmt"/>
        <line num="398" count="0" type="stmt"/>
        <line num="401" count="2" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="403" count="0" type="stmt"/>
        <line num="409" count="0" type="stmt"/>
        <line num="410" count="0" type="stmt"/>
        <line num="411" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="419" count="0" type="stmt"/>
        <line num="420" count="0" type="stmt"/>
        <line num="422" count="0" type="stmt"/>
        <line num="427" count="0" type="stmt"/>
        <line num="428" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="433" count="0" type="stmt"/>
      </file>
    </package>
    <package name="implementation.src">
      <metrics statements="216" coveredstatements="195" conditionals="103" coveredconditionals="86" methods="35" coveredmethods="31"/>
      <file name="localtail.js" path="/Users/<USER>/repos/personal/exp_web_localtail/implementation/src/localtail.js">
        <metrics statements="216" coveredstatements="195" conditionals="103" coveredconditionals="86" methods="35" coveredmethods="31"/>
        <line num="16" count="49" type="stmt"/>
        <line num="17" count="49" type="stmt"/>
        <line num="18" count="49" type="stmt"/>
        <line num="27" count="49" type="stmt"/>
        <line num="28" count="49" type="stmt"/>
        <line num="29" count="49" type="stmt"/>
        <line num="30" count="49" type="stmt"/>
        <line num="31" count="49" type="stmt"/>
        <line num="32" count="49" type="stmt"/>
        <line num="37" count="49" type="cond" truecount="5" falsecount="0"/>
        <line num="38" count="9" type="stmt"/>
        <line num="41" count="40" type="stmt"/>
        <line num="43" count="40" type="stmt"/>
        <line num="44" count="36" type="stmt"/>
        <line num="45" count="36" type="stmt"/>
        <line num="48" count="36" type="stmt"/>
        <line num="51" count="35" type="stmt"/>
        <line num="53" count="35" type="stmt"/>
        <line num="55" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="56" count="3" type="stmt"/>
        <line num="58" count="2" type="stmt"/>
        <line num="63" count="36" type="stmt"/>
        <line num="64" count="36" type="stmt"/>
        <line num="67" count="35" type="stmt"/>
        <line num="69" count="35" type="cond" truecount="2" falsecount="0"/>
        <line num="70" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="72" count="1" type="stmt"/>
        <line num="75" count="34" type="stmt"/>
        <line num="78" count="34" type="cond" truecount="2" falsecount="0"/>
        <line num="79" count="1" type="stmt"/>
        <line num="80" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="81" count="0" type="stmt"/>
        <line num="83" count="1" type="stmt"/>
        <line num="88" count="34" type="cond" truecount="4" falsecount="0"/>
        <line num="89" count="1" type="stmt"/>
        <line num="91" count="20971" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="93" count="1" type="cond" truecount="3" falsecount="1"/>
        <line num="94" count="1" type="stmt"/>
        <line num="98" count="34" type="stmt"/>
        <line num="99" count="34" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="107" count="35" type="stmt"/>
        <line num="108" count="4013" type="cond" truecount="2" falsecount="0"/>
        <line num="109" count="1" type="stmt"/>
        <line num="112" count="34" type="stmt"/>
        <line num="116" count="35" type="stmt"/>
        <line num="122" count="35" type="stmt"/>
        <line num="123" count="39" type="stmt"/>
        <line num="124" count="39" type="stmt"/>
        <line num="127" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="128" count="1" type="stmt"/>
        <line num="133" count="35" type="stmt"/>
        <line num="135" count="5" type="cond" truecount="2" falsecount="0"/>
        <line num="136" count="1" type="stmt"/>
        <line num="142" count="37" type="stmt"/>
        <line num="143" count="37" type="stmt"/>
        <line num="145" count="37" type="cond" truecount="2" falsecount="0"/>
        <line num="147" count="13" type="stmt"/>
        <line num="148" count="13" type="stmt"/>
        <line num="149" count="13" type="stmt"/>
        <line num="150" count="13" type="stmt"/>
        <line num="151" count="13" type="stmt"/>
        <line num="153" count="13" type="stmt"/>
        <line num="160" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="161" count="5" type="stmt"/>
        <line num="164" count="13" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="169" count="0" type="stmt"/>
        <line num="175" count="13" type="stmt"/>
        <line num="176" count="13" type="stmt"/>
        <line num="182" count="13" type="stmt"/>
        <line num="183" count="13" type="stmt"/>
        <line num="184" count="28" type="stmt"/>
        <line num="187" count="13" type="stmt"/>
        <line num="188" count="13" type="stmt"/>
        <line num="191" count="13" type="stmt"/>
        <line num="196" count="2" type="stmt"/>
        <line num="198" count="2" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="stmt"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="222" count="2" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="233" count="26" type="cond" truecount="1" falsecount="1"/>
        <line num="234" count="26" type="stmt"/>
        <line num="235" count="26" type="stmt"/>
        <line num="238" count="26" type="stmt"/>
        <line num="249" count="37" type="stmt"/>
        <line num="250" count="37" type="stmt"/>
        <line num="252" count="31" type="stmt"/>
        <line num="262" count="1" type="stmt"/>
        <line num="263" count="0" type="stmt"/>
        <line num="264" count="22" type="stmt"/>
        <line num="265" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="275" count="29" type="stmt"/>
        <line num="276" count="29" type="stmt"/>
        <line num="281" count="29" type="stmt"/>
        <line num="282" count="29" type="stmt"/>
        <line num="289" count="29" type="cond" truecount="4" falsecount="0"/>
        <line num="290" count="4" type="stmt"/>
        <line num="293" count="25" type="stmt"/>
        <line num="294" count="25" type="stmt"/>
        <line num="297" count="25" type="stmt"/>
        <line num="298" count="25" type="cond" truecount="2" falsecount="0"/>
        <line num="299" count="15" type="stmt"/>
        <line num="300" count="15" type="stmt"/>
        <line num="301" count="15" type="stmt"/>
        <line num="308" count="15" type="stmt"/>
        <line num="312" count="10" type="stmt"/>
        <line num="313" count="10" type="cond" truecount="2" falsecount="0"/>
        <line num="314" count="6" type="stmt"/>
        <line num="315" count="6" type="stmt"/>
        <line num="316" count="6" type="stmt"/>
        <line num="323" count="6" type="stmt"/>
        <line num="324" count="6" type="stmt"/>
        <line num="332" count="4" type="cond" truecount="2" falsecount="0"/>
        <line num="333" count="2" type="stmt"/>
        <line num="336" count="2" type="stmt"/>
        <line num="343" count="18" type="stmt"/>
        <line num="344" count="18" type="stmt"/>
        <line num="346" count="18" type="stmt"/>
        <line num="347" count="180" type="cond" truecount="2" falsecount="0"/>
        <line num="348" count="150" type="stmt"/>
        <line num="352" count="18" type="stmt"/>
        <line num="359" count="58220" type="stmt"/>
        <line num="360" count="58220" type="cond" truecount="1" falsecount="1"/>
        <line num="361" count="58220" type="stmt"/>
        <line num="363" count="0" type="stmt"/>
        <line num="371" count="21" type="stmt"/>
        <line num="372" count="17520" type="cond" truecount="2" falsecount="0"/>
        <line num="373" count="2" type="stmt"/>
        <line num="376" count="19" type="stmt"/>
        <line num="383" count="18" type="stmt"/>
        <line num="384" count="18" type="stmt"/>
        <line num="386" count="18" type="stmt"/>
        <line num="387" count="37169" type="stmt"/>
        <line num="388" count="37169" type="stmt"/>
        <line num="390" count="37169" type="cond" truecount="1" falsecount="1"/>
        <line num="391" count="37169" type="stmt"/>
        <line num="394" count="37169" type="stmt"/>
        <line num="397" count="18" type="stmt"/>
        <line num="404" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="405" count="0" type="stmt"/>
        <line num="408" count="18" type="stmt"/>
        <line num="409" count="18" type="stmt"/>
        <line num="411" count="18" type="stmt"/>
        <line num="412" count="37169" type="stmt"/>
        <line num="413" count="37169" type="cond" truecount="3" falsecount="1"/>
        <line num="414" count="0" type="stmt"/>
        <line num="415" count="0" type="stmt"/>
        <line num="419" count="18" type="stmt"/>
        <line num="426" count="3" type="stmt"/>
        <line num="427" count="3" type="stmt"/>
        <line num="428" count="3" type="stmt"/>
        <line num="429" count="3" type="stmt"/>
        <line num="431" count="3" type="stmt"/>
        <line num="432" count="210" type="cond" truecount="1" falsecount="1"/>
        <line num="434" count="210" type="stmt"/>
        <line num="435" count="210" type="stmt"/>
        <line num="436" count="210" type="stmt"/>
        <line num="439" count="3" type="stmt"/>
        <line num="455" count="35" type="cond" truecount="5" falsecount="0"/>
        <line num="456" count="3" type="stmt"/>
        <line num="459" count="32" type="stmt"/>
        <line num="461" count="32" type="stmt"/>
        <line num="464" count="29" type="stmt"/>
        <line num="467" count="29" type="stmt"/>
        <line num="468" count="21" type="cond" truecount="3" falsecount="0"/>
        <line num="469" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="472" count="21" type="stmt"/>
        <line num="475" count="21" type="stmt"/>
        <line num="476" count="21" type="cond" truecount="2" falsecount="0"/>
        <line num="477" count="2" type="stmt"/>
        <line num="480" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="483" count="19" type="cond" truecount="2" falsecount="0"/>
        <line num="484" count="1" type="stmt"/>
        <line num="493" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="494" count="0" type="stmt"/>
        <line num="497" count="1" type="stmt"/>
        <line num="501" count="18" type="stmt"/>
        <line num="502" count="18" type="stmt"/>
        <line num="503" count="18" type="stmt"/>
        <line num="505" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="507" count="3" type="stmt"/>
        <line num="508" count="3" type="stmt"/>
        <line num="512" count="18" type="stmt"/>
        <line num="515" count="18" type="stmt"/>
        <line num="518" count="18" type="stmt"/>
        <line num="521" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="522" count="5" type="stmt"/>
        <line num="523" count="5" type="stmt"/>
        <line num="524" count="21051" type="stmt"/>
        <line num="525" count="21051" type="cond" truecount="1" falsecount="1"/>
        <line num="526" count="21051" type="cond" truecount="2" falsecount="0"/>
        <line num="528" count="0" type="stmt"/>
        <line num="530" count="5" type="stmt"/>
        <line num="534" count="18" type="stmt"/>
        <line num="535" count="18" type="cond" truecount="3" falsecount="1"/>
        <line num="536" count="18" type="stmt"/>
        <line num="537" count="18" type="cond" truecount="1" falsecount="1"/>
        <line num="538" count="18" type="stmt"/>
        <line num="542" count="18" type="stmt"/>
        <line num="553" count="18" type="cond" truecount="2" falsecount="0"/>
        <line num="554" count="5" type="stmt"/>
        <line num="557" count="18" type="stmt"/>
        <line num="560" count="13" type="cond" truecount="2" falsecount="0"/>
        <line num="561" count="2" type="stmt"/>
        <line num="563" count="11" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
