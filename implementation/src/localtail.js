/**
 * Local file tailer implementation
 * Provides real-time file tailing with efficient seeking and updates
 */

import fs from 'fs/promises';
import { createReadStream } from 'fs';
import { watch } from 'chokidar';
import { EventEmitter } from 'events';

/**
 * File tailer class that handles file watching and content updates
 */
class FileTailer extends EventEmitter {
  constructor(filePath, options = {}) {
    super();
    this.filePath = filePath;
    this.options = {
      encoding: 'utf8',
      maxLines: null,
      fromEnd: false,
      lines: null,
      pollInterval: 100,
      onUpdate: null,
      ...options
    };
    this.position = 0;
    this.content = '';
    this.size = 0;
    this.lastModified = null;
    this.watcher = null;
    this.isBinary = false;
  }

  async initialize() {
    // Validate file path
    if (!this.filePath || typeof this.filePath !== 'string' || this.filePath.trim() === '') {
      throw new Error('Invalid file path');
    }

    try {
      // Check if file exists and get stats
      const stats = await fs.stat(this.filePath);
      this.size = stats.size;
      this.lastModified = new Date(stats.mtime); // Ensure it's a Date object

      // Read initial content
      await this.readContent();

      // Set up file watching
      this.setupWatcher();

      return this;
    } catch (error) {
      if (error.code === 'ENOENT') {
        throw new Error('File not found');
      }
      throw error;
    }
  }

  async readContent() {
    try {
      const buffer = await fs.readFile(this.filePath);

      // Check if file is binary
      this.isBinary = this.detectBinary(buffer);

      if (this.isBinary) {
        this.content = buffer.toString('hex');
        this.warning = 'Binary file detected - content shown as hex';
        return;
      }

      let content = buffer.toString(this.options.encoding);

      // Handle maxLines option
      if (this.options.maxLines) {
        const lines = content.split('\n');
        if (this.options.fromEnd) {
          content = lines.slice(-this.options.maxLines).join('\n');
        } else {
          content = lines.slice(0, this.options.maxLines).join('\n');
        }
      }

      // Handle fromEnd with lines option
      if (this.options.fromEnd && this.options.lines) {
        const lines = content.split('\n');
        // Filter out empty lines at the end and take the requested number
        const nonEmptyLines = lines.filter(line => line.trim() !== '');
        content = nonEmptyLines.slice(-this.options.lines).join('\n');
        if (content && !content.endsWith('\n')) {
          content += '\n';
        }
      }

      this.content = content;
      this.position = buffer.length;
    } catch (error) {
      throw new Error(`Failed to read file: ${error.message}`);
    }
  }

  detectBinary(buffer) {
    // Simple binary detection - check for null bytes
    for (let i = 0; i < Math.min(buffer.length, 1024); i++) {
      if (buffer[i] === 0) {
        return true;
      }
    }
    return false;
  }

  setupWatcher() {
    this.watcher = watch(this.filePath, {
      persistent: true,
      usePolling: false,
      interval: this.options.pollInterval
    });

    this.watcher.on('change', async () => {
      try {
        await this.handleFileChange();
      } catch (error) {
        // Only emit error if we have listeners to prevent unhandled errors
        if (this.listenerCount('error') > 0) {
          this.emit('error', error);
        }
      }
    });

    this.watcher.on('unlink', () => {
      // Only emit error if we have listeners to prevent unhandled errors
      if (this.listenerCount('error') > 0) {
        this.emit('error', new Error('File no longer exists'));
      }
    });
  }

  async handleFileChange() {
    try {
      const stats = await fs.stat(this.filePath);

      if (stats.size > this.position) {
        // File has grown, read new content from our current position
        const newContent = await this.readNewContent(this.position, stats.size);
        this.content += newContent;
        this.size = stats.size;
        this.position = stats.size;
        this.lastModified = new Date(stats.mtime);

        const update = {
          newContent,
          totalContent: this.content,
          position: this.position,
          size: this.size
        };

        if (this.options.onUpdate) {
          this.options.onUpdate(update);
        }

        this.emit('update', update);
      }
    } catch (error) {
      // Only emit error if we have listeners to prevent unhandled errors
      if (this.listenerCount('error') > 0) {
        this.emit('error', error);
      }
    }
  }

  async readNewContent(start, end) {
    return new Promise((resolve, reject) => {
      const stream = createReadStream(this.filePath, {
        start,
        end: end - 1,
        encoding: this.options.encoding
      });

      let content = '';
      stream.on('data', chunk => {
        content += chunk;
      });

      stream.on('end', () => {
        resolve(content);
      });

      stream.on('error', reject);
    });
  }

  async getUpdates() {
    try {
      // Force a fresh stat check to ensure we get the latest file size
      const stats = await fs.stat(this.filePath);

      if (stats.size > this.position) {
        const newContent = await this.readNewContent(this.position, stats.size);
        this.content += newContent;
        this.size = stats.size;
        this.position = stats.size;
        this.lastModified = new Date(stats.mtime);

        return {
          newContent,
          totalContent: this.content,
          position: this.position,
          size: this.size
        };
      }

      return {
        newContent: '',
        totalContent: this.content,
        position: this.position,
        size: this.size
      };
    } catch (error) {
      return {
        error: `File no longer exists: ${error.message}`
      };
    }
  }

  handleError(error) {
    this.emit('error', error);
  }

  close() {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
    }
    // Remove all listeners to prevent memory leaks
    this.removeAllListeners();
  }
}

/**
 * Tail a local file with real-time updates
 * @param {string} filePath - Path to the file to tail
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Tailer object with file content and methods
 */
export async function localtail(filePath, options = {}) {
  const tailer = new FileTailer(filePath, options);
  await tailer.initialize();

  return {
    content: tailer.content,
    position: tailer.position,
    filePath: tailer.filePath,
    size: tailer.size,
    lastModified: tailer.lastModified,
    isBinary: tailer.isBinary,
    warning: tailer.warning,
    encoding: tailer.options.encoding,
    pollInterval: tailer.options.pollInterval,
    getUpdates: () => tailer.getUpdates(),
    handleError: (error) => tailer.handleError(error),
    close: () => tailer.close(),
    on: (event, callback) => tailer.on(event, callback),
    off: (event, callback) => tailer.off(event, callback)
  };
}

/**
 * Time-based file seeker class for efficient timestamp-based seeking
 */
class TimeSeeker {
  constructor(filePath, options = {}) {
    this.filePath = filePath;
    this.options = {
      encoding: 'utf8',
      timestampRegex: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z/,
      ...options
    };
    this.timestampIndex = new Map(); // position -> timestamp
    this.indexBuilt = false;
  }

  /**
   * Parse relative time expression into a target timestamp
   */
  parseTimeExpression(timeExpression) {
    if (!timeExpression || typeof timeExpression !== 'string') {
      throw new Error('Invalid time expression');
    }

    const expr = timeExpression.trim().toLowerCase();
    const now = new Date();

    // Handle "X ago" format
    const agoMatch = expr.match(/^(\d+)\s+(second|minute|hour|day)s?\s+ago$/);
    if (agoMatch) {
      const [, amount, unit] = agoMatch;
      const value = parseInt(amount);
      const multipliers = {
        second: 1000,
        minute: 60 * 1000,
        hour: 60 * 60 * 1000,
        day: 24 * 60 * 60 * 1000
      };

      return new Date(now.getTime() - (value * multipliers[unit]));
    }

    // Handle "last X" format
    const lastMatch = expr.match(/^last\s+(\d+)\s+(second|minute|hour|day)s?$/);
    if (lastMatch) {
      const [, amount, unit] = lastMatch;
      const value = parseInt(amount);
      const multipliers = {
        second: 1000,
        minute: 60 * 1000,
        hour: 60 * 60 * 1000,
        day: 24 * 60 * 60 * 1000
      };

      const startTime = new Date(now.getTime() - (value * multipliers[unit]));
      return {
        type: 'range',
        start: startTime,
        end: now
      };
    }

    // Handle future time expressions
    if (expr.includes('from now')) {
      throw new Error('Cannot seek to future time');
    }

    throw new Error('Invalid time expression');
  }

  /**
   * Detect if file contains timestamps
   */
  async detectTimestamps(content) {
    const lines = content.split('\n').slice(0, 10); // Check first 10 lines
    let timestampCount = 0;

    for (const line of lines) {
      if (this.options.timestampRegex.test(line)) {
        timestampCount++;
      }
    }

    return timestampCount > 0;
  }

  /**
   * Extract timestamp from a line
   */
  extractTimestamp(line) {
    const match = line.match(this.options.timestampRegex);
    if (match) {
      return new Date(match[0]);
    }
    return null;
  }

  /**
   * Detect if buffer contains binary data
   */
  detectBinary(buffer) {
    // Simple binary detection - check for null bytes
    for (let i = 0; i < Math.min(buffer.length, 1024); i++) {
      if (buffer[i] === 0) {
        return true;
      }
    }
    return false;
  }

  /**
   * Build timestamp index for efficient seeking
   */
  async buildTimestampIndex(content) {
    const lines = content.split('\n');
    let position = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const timestamp = this.extractTimestamp(line);

      if (timestamp) {
        this.timestampIndex.set(position, timestamp);
      }

      position += Buffer.byteLength(line + '\n', this.options.encoding);
    }

    this.indexBuilt = true;
  }

  /**
   * Find position closest to target time
   */
  findPositionByTime(targetTime) {
    if (!this.indexBuilt) {
      return 0;
    }

    let bestPosition = 0;
    let bestTimeDiff = Infinity;

    for (const [position, timestamp] of this.timestampIndex) {
      const timeDiff = Math.abs(timestamp.getTime() - targetTime.getTime());
      if (timeDiff < bestTimeDiff && timestamp <= targetTime) {
        bestPosition = position;
        bestTimeDiff = timeDiff;
      }
    }

    return bestPosition;
  }

  /**
   * Inject timestamps for files without native timestamps
   */
  async injectTimestamps(content, fileStats) {
    const lines = content.split('\n');
    const now = new Date();
    const fileAge = now.getTime() - fileStats.mtime.getTime();
    const timePerLine = fileAge / lines.length;

    const timestampedLines = lines.map((line, index) => {
      if (line.trim() === '') return line;

      const lineTime = new Date(now.getTime() - (fileAge - (index * timePerLine)));
      const timestamp = lineTime.toISOString();
      return `${timestamp} ${line}`;
    });

    return timestampedLines.join('\n');
  }
}

/**
 * Seek to a relative time in a file and return content from that point
 * @param {string} filePath - Path to the file to seek in
 * @param {string} timeExpression - Relative time expression (e.g., "5 minutes ago", "last 10 minutes")
 * @param {Object} options - Configuration options
 * @returns {Promise<Object>} Seek result with content and metadata
 */
// Export the FileTailer class for testing
export { FileTailer };

export async function localtail_seek_relative_time(filePath, timeExpression, options = {}) {
  // Validate file path
  if (!filePath || typeof filePath !== 'string' || filePath.trim() === '') {
    throw new Error('Invalid file path');
  }

  try {
    // Check if file exists and get stats
    const stats = await fs.stat(filePath);

    // Create time seeker
    const seeker = new TimeSeeker(filePath, options);

    // Parse time expression
    const timeResult = seeker.parseTimeExpression(timeExpression);
    const isRange = timeResult && typeof timeResult === 'object' && timeResult.type === 'range';
    const targetTime = isRange ? timeResult.start : timeResult;

    // Read file content
    const buffer = await fs.readFile(filePath);

    // Check if file is binary
    const isBinary = seeker.detectBinary(buffer);
    if (isBinary) {
      throw new Error('Cannot seek in binary files');
    }

    const content = buffer.toString(options.encoding || 'utf8');

    // Handle empty files
    if (content.trim() === '') {
      const result = {
        content: '',
        seekTime: targetTime,
        position: 0,
        size: 0,
        warning: 'File is empty',
        close: () => {}
      };

      if (isRange) {
        result.timeRange = timeResult;
      }

      return result;
    }

    // Detect if file has timestamps
    const hasTimestamps = await seeker.detectTimestamps(content);
    let processedContent = content;
    let timestampsInjected = false;

    if (!hasTimestamps) {
      // Inject timestamps
      processedContent = await seeker.injectTimestamps(content, stats);
      timestampsInjected = true;
    }

    // Build timestamp index
    await seeker.buildTimestampIndex(processedContent);

    // Find position for target time
    const seekPosition = seeker.findPositionByTime(targetTime);

    // Extract content from seek position
    let seekContent = processedContent.substring(seekPosition);

    // For ranges, filter content within the time range
    if (isRange) {
      const lines = seekContent.split('\n');
      const filteredLines = lines.filter(line => {
        const timestamp = seeker.extractTimestamp(line);
        if (timestamp) {
          return timestamp >= timeResult.start && timestamp <= timeResult.end;
        }
        return true; // Include lines without timestamps
      });
      seekContent = filteredLines.join('\n');
    }

    // Handle seeking beyond file start
    let warning = null;
    if (seekPosition === 0 && seeker.timestampIndex.size > 0) {
      const firstTimestamp = Array.from(seeker.timestampIndex.values())[0];
      if (targetTime < firstTimestamp) {
        warning = 'Seeking beyond file start - showing from beginning';
      }
    }

    const result = {
      content: seekContent,
      seekTime: targetTime,
      position: seekPosition,
      size: stats.size,
      timestampsInjected,
      indexUsed: seeker.indexBuilt,
      warning,
      close: () => {}
    };

    if (isRange) {
      result.timeRange = timeResult;
    }

    return result;

  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new Error('File not found');
    }
    throw error;
  }
}
