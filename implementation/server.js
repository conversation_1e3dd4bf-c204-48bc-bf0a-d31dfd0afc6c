#!/usr/bin/env node

/**
 * Minimal HTTP server for the File Tailer UI
 * Serves static files and provides CORS headers for local development
 */

import express from 'express';
import { createServer } from 'http';
import { WebSocketServer } from 'ws';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs/promises';
import { localtail, localtail_seek_relative_time } from './src/localtail.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// Store active tailers and their state
const activeTailers = new Map();
const tailerStates = new Map(); // Track position and content for each tailer

// NEW: Store internal timers and timestamp metadata for each file
const tailerTimers = new Map(); // Track internal timers for time-based functionality
const tailerMetadata = new Map(); // Store timestamp metadata (not exposed to client)

// Enable CORS for local development
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  next();
});

// Parse JSON bodies
app.use(express.json());

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// Serve source files for ES module imports
app.use('/src', express.static(path.join(__dirname, 'src')));

// Serve the main page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

/**
 * Parse relative time expression into a target timestamp or time range
 * This function is used for internal timer-based seeking
 */
function parseTimeExpression(timeExpression) {
  if (!timeExpression || typeof timeExpression !== 'string') {
    throw new Error('Invalid time expression');
  }

  const expr = timeExpression.trim().toLowerCase();
  const now = new Date();

  // Handle "X ago" format
  const agoMatch = expr.match(/^(\d+)\s+(second|minute|hour|day)s?\s+ago$/);
  if (agoMatch) {
    const [, amount, unit] = agoMatch;
    const value = parseInt(amount);
    const multipliers = {
      second: 1000,
      minute: 60 * 1000,
      hour: 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000
    };

    return new Date(now.getTime() - (value * multipliers[unit]));
  }

  // Handle "last X" format
  const lastMatch = expr.match(/^last\s+(\d+)\s+(second|minute|hour|day)s?$/);
  if (lastMatch) {
    const [, amount, unit] = lastMatch;
    const value = parseInt(amount);
    const multipliers = {
      second: 1000,
      minute: 60 * 1000,
      hour: 60 * 60 * 1000,
      day: 24 * 60 * 60 * 1000
    };

    const startTime = new Date(now.getTime() - (value * multipliers[unit]));
    return {
      type: 'range',
      start: startTime,
      end: now
    };
  }

  // Handle future time expressions
  if (expr.includes('from now')) {
    throw new Error('Cannot seek to future time');
  }

  throw new Error('Invalid time expression');
}

// API endpoint to list available files
app.get('/api/files', async (req, res) => {
  try {
    const files = await fs.readdir(__dirname);
    const logFiles = files.filter(file =>
      file.endsWith('.log') || file.endsWith('.txt')
    );
    res.json({ files: logFiles });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// API endpoint to start tailing a file
app.post('/api/tail/start', async (req, res) => {
  try {
    const { filePath } = req.body;
    if (!filePath) {
      return res.status(400).json({ error: 'File path is required' });
    }

    const fullPath = path.resolve(__dirname, filePath);

    // Check if file exists
    await fs.access(fullPath);

    // Stop existing tailer if any
    if (activeTailers.has(filePath)) {
      const existingTailer = activeTailers.get(filePath);
      existingTailer.close();

      // NEW: Stop existing timer and cleanup metadata
      if (tailerTimers.has(filePath)) {
        clearInterval(tailerTimers.get(filePath));
        tailerTimers.delete(filePath);
      }
      tailerMetadata.delete(filePath);
    }

    // Start new tailer without file watcher to avoid conflicts
    const tailer = await localtail(fullPath);
    activeTailers.set(filePath, tailer);

    // Store initial state
    tailerStates.set(filePath, {
      lastPosition: tailer.position,
      lastSize: tailer.size,
      lastContent: tailer.content
    });

    // NEW: Start internal timer for timestamp tracking
    const startTime = Date.now();
    const timer = setInterval(() => {
      // Timer is running - this enables stateful time tracking
      // The timer itself doesn't need to do anything, it just needs to exist
      // to indicate that this file is being actively tracked with timestamps
    }, 1000); // Update every second for time tracking

    tailerTimers.set(filePath, timer);

    // NEW: Initialize timestamp metadata for the file
    // This metadata is stored in memory and never exposed to the client
    const lines = tailer.content.split('\n').filter(line => line.trim() !== ''); // Filter out empty lines
    tailerMetadata.set(filePath, {
      startTime,
      lines: lines.map((line, index) => ({
        content: line,
        // Assign timestamps based on when tailing started
        // In a real implementation, this could be more sophisticated
        timestamp: new Date(startTime + (index * 100)) // 100ms between lines
      }))
    });

    res.json({
      success: true,
      filePath,
      initialContent: tailer.content,
      size: tailer.size,
      position: tailer.position
    });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// API endpoint to get file updates
app.get('/api/tail/updates/:filePath', async (req, res) => {
  try {
    const { filePath } = req.params;
    const decodedFilePath = decodeURIComponent(filePath);
    const tailer = activeTailers.get(decodedFilePath);
    const state = tailerStates.get(decodedFilePath);

    console.log(`📡 Update request for: ${decodedFilePath}`);

    if (!tailer || !state) {
      console.log(`❌ No tailer found for: ${decodedFilePath}`);
      return res.status(404).json({ error: 'No active tailer for this file' });
    }

    // Check file size directly to detect changes
    const stats = await fs.stat(path.resolve(__dirname, decodedFilePath));
    const currentSize = stats.size;

    let newContent = '';

    if (currentSize > state.lastSize) {
      // File has grown, read the new content
      const fullContent = await fs.readFile(path.resolve(__dirname, decodedFilePath), 'utf8');
      newContent = fullContent.substring(state.lastPosition);

      // Update state
      state.lastPosition = currentSize;
      state.lastSize = currentSize;
      state.lastContent = fullContent;

      // NEW: Update timestamp metadata for new content
      if (tailerMetadata.has(decodedFilePath) && newContent.trim()) {
        const metadata = tailerMetadata.get(decodedFilePath);
        const newLines = newContent.split('\n').filter(line => line.trim() !== ''); // Filter out empty lines
        const currentTime = Date.now();

        // Add timestamp metadata for new lines
        newLines.forEach((line, index) => {
          metadata.lines.push({
            content: line,
            timestamp: new Date(currentTime + (index * 100))
          });
        });
      }
    }

    console.log(`📊 Updates for ${decodedFilePath}:`, {
      hasNewContent: !!newContent,
      newContentLength: newContent.length,
      currentSize,
      lastSize: state.lastSize,
      lastPosition: state.lastPosition
    });

    res.json({
      newContent,
      totalContent: state.lastContent,
      position: state.lastPosition,
      size: state.lastSize
    });

  } catch (error) {
    const { filePath } = req.params;
    const decodedFilePath = decodeURIComponent(filePath);
    console.error(`❌ Error getting updates for ${decodedFilePath}:`, error.message);
    res.status(500).json({ error: error.message });
  }
});

// API endpoint to stop tailing a file
app.post('/api/tail/stop', async (req, res) => {
  try {
    const { filePath } = req.body;
    const tailer = activeTailers.get(filePath);

    if (tailer) {
      tailer.close();
      activeTailers.delete(filePath);
      tailerStates.delete(filePath);

      // NEW: Stop and cleanup timer
      if (tailerTimers.has(filePath)) {
        clearInterval(tailerTimers.get(filePath));
        tailerTimers.delete(filePath);
      }

      // NEW: Cleanup timestamp metadata
      tailerMetadata.delete(filePath);
    }

    res.json({ success: true });

  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// API endpoint for relative time seeking using internal timer metadata
app.post('/api/tail/seek', async (req, res) => {
  try {
    const { filePath, timeExpression } = req.body;

    if (!filePath) {
      return res.status(400).json({ error: 'File path is required' });
    }

    if (!timeExpression) {
      return res.status(400).json({ error: 'Time expression is required' });
    }

    // Check if we have internal metadata for this file
    const metadata = tailerMetadata.get(filePath);
    if (!metadata) {
      return res.status(400).json({
        error: 'File is not being actively tailed. Please start tailing first.'
      });
    }

    // Parse the time expression to get target time range
    const timeResult = parseTimeExpression(timeExpression);
    const isRange = timeResult && typeof timeResult === 'object' && timeResult.type === 'range';

    let filteredLines = [];
    let warning = null;

    if (isRange) {
      // Filter lines within the time range using internal metadata
      filteredLines = metadata.lines.filter(lineData => {
        return lineData.timestamp >= timeResult.start && lineData.timestamp <= timeResult.end;
      });

      if (filteredLines.length === 0) {
        warning = 'No content found in the specified time range';
      }
    } else {
      // For single point in time, find lines after that time
      const targetTime = timeResult;
      filteredLines = metadata.lines.filter(lineData => {
        return lineData.timestamp >= targetTime;
      });

      if (filteredLines.length === 0) {
        warning = 'No content found after the specified time';
      }
    }

    // Extract content from filtered lines
    const content = filteredLines.map(lineData => lineData.content).join('\n');

    res.json({
      success: true,
      content: content,
      seekTime: isRange ? timeResult.start : timeResult,
      timeRange: isRange ? timeResult : undefined,
      warning: warning,
      linesFound: filteredLines.length,
      totalLines: metadata.lines.length,
      startTime: metadata.startTime
    });

  } catch (error) {
    console.error(`❌ Error seeking in file:`, error.message);
    res.status(500).json({ error: error.message });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    activeTailers: activeTailers.size,
    activeTimers: tailerTimers.size, // NEW: Include timer count
    activeMetadata: tailerMetadata.size // NEW: Include metadata count
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Server error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'Not found' });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 File Tailer UI Server running at http://localhost:${PORT}`);
  console.log(`📁 Serving files from: ${__dirname}`);
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log('\n📖 Usage:');
  console.log('1. Open http://localhost:3000 in your browser');
  console.log('2. Enter a file path or use the browse button');
  console.log('3. Click "Start Tailing" to begin monitoring');
  console.log('\n💡 Tip: Create a test log file with:');
  console.log('   echo "Test log entry" > test.log');
  console.log('   echo "Another entry" >> test.log');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  cleanupAndExit();
});

process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  cleanupAndExit();
});

// NEW: Cleanup function to properly close all timers and tailers
function cleanupAndExit() {
  // Close all active tailers
  for (const tailer of activeTailers.values()) {
    try {
      tailer.close();
    } catch (error) {
      console.error('Error closing tailer:', error.message);
    }
  }

  // Clear all timers
  for (const timer of tailerTimers.values()) {
    try {
      clearInterval(timer);
    } catch (error) {
      console.error('Error clearing timer:', error.message);
    }
  }

  // Clear all maps
  activeTailers.clear();
  tailerStates.clear();
  tailerTimers.clear();
  tailerMetadata.clear();

  console.log('✅ Cleanup completed');
  process.exit(0);
}

export default app;
